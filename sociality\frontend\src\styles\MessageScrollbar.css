/**
 * MessageContainer Scrollbar Styles
 * Custom scrollbar styling for the message container to prevent flickering
 * and provide a smooth scrolling experience
 */

/* React Window List with hidden scrollbar - overlay style */
.react-window-list {
  /* Hide default scrollbar completely */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  /* Ensure full height utilization */
  flex: 1 1 0 !important;
  min-height: 0 !important;
  /* Remove any default margins/padding */
  margin: 0 !important;
  padding: 0 !important;
  /* Enable smooth scrolling */
  scroll-behavior: smooth;
  position: relative;
}

/* Hide webkit scrollbar completely */
.react-window-list::-webkit-scrollbar {
  display: none;
  width: 0;
  background: transparent;
}

/* Remove unused overlay classes that could interfere with message visibility */

/* Smooth scrolling behavior */
.react-window-list {
  scroll-behavior: smooth;
}

/* Prevent scrollbar from causing layout shifts */
.message-scrollbar-container {
  scrollbar-gutter: stable;
  /* Ensure full height utilization */
  flex: 1 1 0 !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

.message-container {
  scrollbar-gutter: stable;
  /* Maximize available space */
  flex: 1 1 0 !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Optimize message item rendering to prevent flickering */
.message-item {
  /* Reduced optimizations to prevent visual issues */
  will-change: auto;
}

/* Reduce animation complexity for better performance */
.message-new {
  animation: messageSlideIn 0.2s ease-out forwards !important;
  animation-fill-mode: both !important;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent layout thrashing during scroll - removed aggressive containment */

/* Optimize for mobile scrolling */
@media (max-width: 768px) {
  .react-window-list::-webkit-scrollbar {
    width: 4px;
  }
  
  /* Reduce animation on mobile for better performance */
  .message-new {
    animation: none !important;
  }
  
  .message-item {
    transition: none !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .react-window-list::-webkit-scrollbar {
    width: 8px;
  }
}
