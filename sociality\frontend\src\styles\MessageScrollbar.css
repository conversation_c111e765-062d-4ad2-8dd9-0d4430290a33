/**
 * MessageContainer Scrollbar Styles
 * Custom scrollbar styling for the message container to prevent flickering
 * and provide a smooth scrolling experience
 */

/* React Window List with hidden scrollbar - overlay style */
.react-window-list {
  /* Hide default scrollbar completely */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  /* Ensure full height utilization */
  flex: 1 1 0 !important;
  min-height: 0 !important;
  /* Remove any default margins/padding */
  margin: 0 !important;
  padding: 0 !important;
  /* Enable smooth scrolling */
  scroll-behavior: smooth;
  position: relative;
}

/* Hide webkit scrollbar completely */
.react-window-list::-webkit-scrollbar {
  display: none;
  width: 0;
  background: transparent;
}

/* Custom overlay scrollbar indicator */
.message-scroll-indicator {
  position: absolute;
  right: 2px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  pointer-events: none;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-scroll-indicator.visible {
  opacity: 1;
}

.message-scroll-indicator::after {
  content: '';
  position: absolute;
  right: 0;
  background: rgba(0, 0, 0, 0.3);
  width: 3px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

/* Dark mode custom scrollbar indicator */
[data-theme="dark"] .message-scroll-indicator::after,
.chakra-ui-dark .message-scroll-indicator::after {
  background: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .message-scroll-indicator:hover::after,
.chakra-ui-dark .message-scroll-indicator:hover::after {
  background: rgba(255, 255, 255, 0.5);
}

/* Light mode custom scrollbar indicator */
.message-scroll-indicator::after {
  background: rgba(0, 0, 0, 0.3);
}

.message-scroll-indicator:hover::after {
  background: rgba(0, 0, 0, 0.5);
}

/* Smooth scrolling behavior */
.react-window-list {
  scroll-behavior: smooth;
}

/* Prevent scrollbar from causing layout shifts */
.message-scrollbar-container {
  scrollbar-gutter: stable;
  contain: layout style;
  /* Ensure full height utilization */
  flex: 1 1 0 !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

.message-container {
  scrollbar-gutter: stable;
  /* Maximize available space */
  flex: 1 1 0 !important;
  min-height: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Optimize message item rendering to prevent flickering */
.message-item {
  contain: layout style;
  will-change: auto;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Reduce animation complexity for better performance */
.message-new {
  animation: messageSlideIn 0.2s ease-out forwards !important;
  animation-fill-mode: both !important;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent layout thrashing during scroll */
.react-window-list > div {
  contain: layout;
}

/* Optimize for mobile scrolling */
@media (max-width: 768px) {
  .react-window-list::-webkit-scrollbar {
    width: 4px;
  }
  
  /* Reduce animation on mobile for better performance */
  .message-new {
    animation: none !important;
  }
  
  .message-item {
    transition: none !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .react-window-list::-webkit-scrollbar {
    width: 8px;
  }
}
