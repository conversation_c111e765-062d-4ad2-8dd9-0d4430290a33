/**
 * MessageContainer Scroll<PERSON> Styles
 * Custom scrollbar styling for the message container to prevent flickering
 * and provide a smooth scrolling experience
 */

/* React Window List scrollbar styling */
.react-window-list {
  /* Webkit browsers (Chrome, Safari, Edge) */
  scrollbar-width: thin;
  scrollbar-gutter: stable;
}

.react-window-list::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

.react-window-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.react-window-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.react-window-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Dark mode scrollbar */
[data-theme="dark"] .react-window-list::-webkit-scrollbar-thumb,
.chakra-ui-dark .react-window-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .react-window-list::-webkit-scrollbar-thumb:hover,
.chakra-ui-dark .react-window-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Firefox scrollbar colors */
[data-theme="dark"] .react-window-list,
.chakra-ui-dark .react-window-list {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.react-window-list {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Smooth scrolling behavior */
.react-window-list {
  scroll-behavior: smooth;
}

/* Prevent scrollbar from causing layout shifts */
.message-scrollbar-container {
  scrollbar-gutter: stable;
  contain: layout style;
}

.message-container {
  scrollbar-gutter: stable;
}

/* Optimize message item rendering to prevent flickering */
.message-item {
  contain: layout style;
  will-change: auto;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Reduce animation complexity for better performance */
.message-new {
  animation: messageSlideIn 0.2s ease-out forwards !important;
  animation-fill-mode: both !important;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent layout thrashing during scroll */
.react-window-list > div {
  contain: layout;
}

/* Optimize for mobile scrolling */
@media (max-width: 768px) {
  .react-window-list::-webkit-scrollbar {
    width: 4px;
  }
  
  /* Reduce animation on mobile for better performance */
  .message-new {
    animation: none !important;
  }
  
  .message-item {
    transition: none !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .react-window-list::-webkit-scrollbar {
    width: 8px;
  }
}
